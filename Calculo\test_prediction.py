#!/usr/bin/env python3
"""
Script para probar las correcciones del sistema de detección de riesgo volcánico
"""

import requests


def test_prediction_system():
    """Prueba el sistema completo de predicción"""
    
    print("🌋 PRUEBA DEL SISTEMA DE DETECCIÓN DE RIESGO VOLCÁNICO")
    print("=" * 60)
    
    # 1. Verificar servicio
    try:
        health = requests.get('http://localhost:8000/health')
        print(f"✅ Servicio disponible: {health.json()['status']}")
    except Exception as e:
        print(f"❌ Error conectando al servicio: {e}")
        return False
    
    # 2. Generar datos de pre-erupción (14 días)
    print("\n🔄 Generando datos de pre-erupción (14 días)...")
    try:
        data_response = requests.get('http://localhost:8000/data/generate?days=14&eruption_scenario=true')
        data = data_response.json()
        print(f"✅ Datos generados: {data['count']} eventos sísmicos")
        print(f"   Período: {data['period_days']} días")
        print(f"   Escenario de erupción: {data['eruption_scenario']}")
    except Exception as e:
        print(f"❌ Error generando datos: {e}")
        return False
    
    # 3. Realizar predicción completa directamente (incluye derivadas + FFM + alertas)
    print("\n🔄 Realizando predicción completa (incluye derivadas, FFM y sistema de alertas)...")
    try:
        prediction_request = {
            'seismic_data': data['data'],
            'time_window': 24,
            'calculation_detail': True
        }

        prediction_response = requests.post('http://localhost:8000/predict',
                                          json=prediction_request)
        prediction = prediction_response.json()

        print("📊 Resultados completos:")
        print(f"   Probabilidad de erupción: {prediction['eruption_probability']:.2%}")
        print(f"   Nivel de alerta: {prediction['alert_level']}")
        print(f"   Tendencia de aceleración: {prediction['derivative_analysis']['acceleration_trend']}")
        print(f"   Máxima aceleración: {prediction['derivative_analysis']['max_acceleration']:.4f}")

    except Exception as e:
        print(f"❌ Error en predicción completa: {e}")
        return False
    
    # 4. Verificar congruencia
    print("\n🔍 VERIFICACIÓN DE CONGRUENCIA:")
    print("=" * 40)
    
    # Verificar que el escenario de pre-erupción genere riesgo alto
    probability = prediction['eruption_probability']
    alert_level = prediction['alert_level']
    acceleration_trend = prediction['derivative_analysis']['acceleration_trend']
    max_acceleration = prediction['derivative_analysis']['max_acceleration']
    
    # Criterios de éxito
    success_criteria = []
    
    # 1. Datos de pre-erupción deben generar aceleración significativa
    if max_acceleration > 2.0:
        success_criteria.append("✅ Aceleración significativa detectada")
    else:
        success_criteria.append(f"❌ Aceleración insuficiente: {max_acceleration:.4f} (esperado > 2.0)")
    
    # 2. Tendencia debe indicar aceleración crítica o moderada
    if "CRÍTICA" in acceleration_trend or "MODERADA" in acceleration_trend:
        success_criteria.append("✅ Tendencia de aceleración correcta")
    else:
        success_criteria.append(f"❌ Tendencia incorrecta: {acceleration_trend}")
    
    # 3. Probabilidad debe ser significativa para pre-erupción
    if probability > 0.35:
        success_criteria.append("✅ Probabilidad de erupción alta")
    else:
        success_criteria.append(f"❌ Probabilidad baja: {probability:.2%} (esperado > 35%)")
    
    # 4. Nivel de alerta debe ser ORANGE o RED
    if alert_level in ['ORANGE', 'RED']:
        success_criteria.append("✅ Nivel de alerta apropiado")
    else:
        success_criteria.append(f"❌ Nivel de alerta bajo: {alert_level} (esperado ORANGE/RED)")
    
    # Mostrar resultados
    for criterion in success_criteria:
        print(criterion)
    
    # Resultado final
    success_count = sum(1 for c in success_criteria if c.startswith("✅"))
    total_count = len(success_criteria)
    
    print(f"\n📈 RESULTADO FINAL: {success_count}/{total_count} criterios cumplidos")
    
    if success_count == total_count:
        print("🎉 ¡CORRECCIÓN EXITOSA! El sistema ahora es congruente.")
        print("   - Los gráficos mostrarán aceleración significativa")
        print("   - El detector mostrará riesgo ALTO/CRÍTICO")
        print("   - Todos los componentes están sincronizados")
        return True
    else:
        print("⚠️  Aún hay incongruencias en el sistema.")
        print("   Revisar la lógica de clasificación y umbrales.")
        return False

if __name__ == "__main__":
    test_prediction_system()
