/**
 * 🌋 Volcano App Frontend - Cálculo Volcánico
 * Vista principal para demostrar cálculos de derivadas volcánicas
 */

import { AlertTriangle, Loader2, Play, RefreshCw, TrendingUp } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useToast } from '../../hooks/use-toast';
import {
    calculoService,
    DerivativeResult,
    PredictionResult,
    SyntheticDataResponse
} from '../../services/calculoService';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { AccelerationDetector } from './AccelerationDetector';
import { DerivativeChart } from './DerivativeChart';

export const CalculoVolcanico: React.FC = () => {
  const { toast } = useToast();

  // Estados principales
  const [syntheticData, setSyntheticData] = useState<SyntheticDataResponse | null>(null);
  const [derivativeResult, setDerivativeResult] = useState<DerivativeResult | null>(null);
  const [predictionResult, setPredictionResult] = useState<PredictionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [serviceHealth, setServiceHealth] = useState<any>(null);

  // Estados de configuración
  const [selectedScenario, setSelectedScenario] = useState<'normal' | 'pre_eruption'>('normal');
  const [dataDays, setDataDays] = useState(7);
  const [showSteps, setShowSteps] = useState(true);

  // Verificar salud del servicio al cargar
  useEffect(() => {
    checkServiceHealth();
  }, []);

  const checkServiceHealth = async () => {
    try {
      const health = await calculoService.checkHealth();
      setServiceHealth(health);
    } catch (error) {
      console.error('Service health check failed:', error);
      setServiceHealth({ status: 'error', message: 'Servicio no disponible' });
    }
  };

  // Generar datos sintéticos
  const generateSyntheticData = async () => {
    setLoading(true);
    try {
      const options = {
        days: dataDays,
        eruption_scenario: selectedScenario === 'pre_eruption',
        pattern_type: selectedScenario === 'pre_eruption' ? 'exponential' as const : 'linear' as const
      };

      const data = await calculoService.generateSyntheticData(options);
      setSyntheticData(data);
      
      // Limpiar resultados anteriores
      setDerivativeResult(null);
      setPredictionResult(null);

      toast({
        title: "Datos generados",
        description: `Se generaron ${data.data.length} puntos de datos sísmicos`
      });
    } catch (error) {
      console.error('Error generating synthetic data:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: calculoService.handleApiError(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // Calcular derivadas
  const calculateDerivatives = async () => {
    if (!syntheticData?.data) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Primero debes generar datos sintéticos"
      });
      return;
    }

    setLoading(true);
    try {
      const result = await calculoService.calculateDerivativesFromSeismic(
        syntheticData.data,
        showSteps
      );
      
      // Usar los resultados del backend directamente (no sobrescribir)
      const extendedResult = {
        ...result,
        // Usar la clasificación del backend
        acceleration_detected: result.acceleration_trend !== "ACTIVIDAD ESTABLE",
        // Convertir la tendencia del backend a formato del frontend
        acceleration_trend: result.acceleration_trend === "ACTIVIDAD ESTABLE" ? 'stable' :
                           result.acceleration_trend.includes("POSITIVA") ? 'increasing' : 'decreasing'
      };
      
      setDerivativeResult(extendedResult);

      toast({
        title: "Cálculo completado",
        description: "Las derivadas han sido calculadas exitosamente"
      });
    } catch (error) {
      console.error('Error calculating derivatives:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: calculoService.handleApiError(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // Realizar predicción completa
  const performPrediction = async () => {
    if (!syntheticData?.data) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Primero debes generar datos sintéticos"
      });
      return;
    }

    setLoading(true);
    try {
      const result = await calculoService.predictEruption({
        seismic_data: syntheticData.data,
        time_window: 24,
        calculation_detail: true
      });
      setPredictionResult(result);

      toast({
        title: "Predicción completada",
        description: `Nivel de alerta: ${result.alert_level}`
      });
    } catch (error) {
      console.error('Error performing prediction:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: calculoService.handleApiError(error)
      });
    } finally {
      setLoading(false);
    }
  };

  // Renderizar indicador de salud del servicio
  const renderServiceHealth = () => {
    if (!serviceHealth) return null;

    const isHealthy = serviceHealth.status === 'healthy' || serviceHealth.status === 'ok';
    
    return (
      <Alert className={isHealthy ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}>
        <AlertTriangle className={`h-4 w-4 ${isHealthy ? 'text-green-600' : 'text-red-600'}`} />
        <AlertDescription className={isHealthy ? 'text-green-700' : 'text-red-700'}>
          <strong>Servicio de Cálculo:</strong> {isHealthy ? 'Operativo' : 'No disponible'}
          {!isHealthy && (
            <span className="ml-2">
              - Verifica que el monolito Python esté ejecutándose en puerto 8000
            </span>
          )}
        </AlertDescription>
      </Alert>
    );
  };

  // Renderizar controles
  const renderControls = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          ⚙️ Configuración y Controles
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Configuración de escenario */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Escenario:</label>
            <div className="flex gap-2">
              <Button
                variant={selectedScenario === 'normal' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedScenario('normal')}
              >
                Actividad Normal
              </Button>
              <Button
                variant={selectedScenario === 'pre_eruption' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedScenario('pre_eruption')}
              >
                Pre-Erupción
              </Button>
            </div>
          </div>

          {/* Configuración de días */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Días de datos:</label>
            <div className="flex gap-2">
              {[3, 7, 14, 30].map(days => (
                <Button
                  key={days}
                  variant={dataDays === days ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setDataDays(days)}
                >
                  {days}d
                </Button>
              ))}
            </div>
          </div>

          {/* Configuración de pasos */}
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Mostrar pasos:</label>
            <Button
              variant={showSteps ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowSteps(!showSteps)}
            >
              {showSteps ? 'Sí' : 'No'}
            </Button>
          </div>

          {/* Botones de acción */}
          <div className="flex gap-2 pt-4">
            <Button
              onClick={generateSyntheticData}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
              Generar Datos
            </Button>
            
            <Button
              onClick={calculateDerivatives}
              disabled={loading || !syntheticData}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <TrendingUp className="h-4 w-4" />}
              Calcular Derivadas
            </Button>
            
            <Button
              onClick={performPrediction}
              disabled={loading || !syntheticData}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
              Predicción Completa
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Renderizar datos sintéticos
  const renderSyntheticData = () => {
    if (!syntheticData) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle>📊 Datos Sintéticos Generados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {syntheticData.count}
              </div>
              <div className="text-sm text-gray-600">Puntos de Datos</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {syntheticData.eruption_scenario ? 'Pre-Erupción' : 'Normal'}
              </div>
              <div className="text-sm text-gray-600">Escenario</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {selectedScenario === 'pre_eruption' ? 'Exponencial' : 'Lineal'}
              </div>
              <div className="text-sm text-gray-600">Patrón</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {syntheticData.period_days}
              </div>
              <div className="text-sm text-gray-600">Días</div>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg">
            <h4 className="font-semibold mb-2">Información de Generación:</h4>
            <p className="text-sm text-gray-600">
              <strong>Generado:</strong> {new Date(syntheticData.generated_at).toLocaleString()}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Período:</strong> {syntheticData.period_days} días de datos sintéticos
            </p>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Renderizar resultado de predicción
  const renderPredictionResult = () => {
    if (!predictionResult) return null;

    const getAlertColor = (level: string) => {
      switch (level) {
        case 'GREEN': return 'bg-green-100 text-green-800';
        case 'YELLOW': return 'bg-yellow-100 text-yellow-800';
        case 'ORANGE': return 'bg-orange-100 text-orange-800';
        case 'RED': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    };

    // Función para mapear nivel de alerta a riesgo
    const getRiskLevel = (alertLevel: string) => {
      switch (alertLevel) {
        case 'RED': return 'CRÍTICO';
        case 'ORANGE': return 'ALTO';
        case 'YELLOW': return 'MEDIO';
        case 'GREEN': return 'BAJO';
        default: return 'DESCONOCIDO';
      }
    };

    // Generar recomendaciones basadas en el nivel de alerta
    const getRecommendations = (alertLevel: string) => {
      switch (alertLevel) {
        case 'RED':
          return [
            'Activar protocolos de emergencia inmediatos',
            'Evacuar zonas de alto riesgo',
            'Monitoreo continuo cada 15 minutos',
            'Contactar autoridades de emergencia'
          ];
        case 'ORANGE':
          return [
            'Incrementar vigilancia intensiva',
            'Preparar planes de evacuación',
            'Monitoreo cada 30 minutos',
            'Alertar a equipos de respuesta'
          ];
        case 'YELLOW':
          return [
            'Mantener monitoreo intensificado',
            'Revisar protocolos de respuesta',
            'Monitoreo cada hora',
            'Informar a autoridades locales'
          ];
        case 'GREEN':
          return [
            'Continuar monitoreo rutinario',
            'Mantener análisis de tendencias',
            'Verificar equipos de medición',
            'Actualizar datos históricos'
          ];
        default:
          return ['Verificar estado del sistema'];
      }
    };

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔮 Resultado de Predicción Completa
            <Badge className={getAlertColor(predictionResult.alert_level)}>
              {predictionResult.alert_level}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {(predictionResult.eruption_probability * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Probabilidad de Erupción</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {predictionResult.ffm_analysis.time_to_failure
                  ? `${predictionResult.ffm_analysis.time_to_failure.toFixed(1)}h`
                  : 'N/A'
                }
              </div>
              <div className="text-sm text-gray-600">Tiempo hasta Falla</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {getRiskLevel(predictionResult.alert_level)}
              </div>
              <div className="text-sm text-gray-600">Nivel de Riesgo</div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="bg-blue-50 p-3 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-1">Análisis FFM</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>Tendencia de Aceleración:</strong> {predictionResult.ffm_analysis.acceleration_trend}</p>
                <p><strong>Nivel de Confianza:</strong> {(predictionResult.ffm_analysis.confidence_level * 100).toFixed(1)}%</p>
                <p><strong>Umbral Crítico:</strong> {predictionResult.ffm_analysis.critical_threshold.toFixed(2)}</p>
              </div>
            </div>

            {predictionResult.ffm_analysis.precursor_indicators.length > 0 && (
              <div className="bg-orange-50 p-3 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-1">Indicadores Precursores</h4>
                <ul className="text-sm text-orange-700 space-y-1">
                  {predictionResult.ffm_analysis.precursor_indicators.map((indicator, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-orange-600">•</span>
                      {indicator}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="bg-yellow-50 p-3 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-1">Recomendaciones</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {getRecommendations(predictionResult.alert_level).map((rec, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-yellow-600">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Encabezado */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          🌋 Análisis de Derivadas Volcánicas
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Demostración del cálculo de derivadas para detectar aceleración sísmica y predecir erupciones volcánicas
        </p>
      </div>

      {/* Salud del servicio */}
      {renderServiceHealth()}

      {/* Controles */}
      {renderControls()}

      {/* Datos sintéticos */}
      {renderSyntheticData()}

      {/* Resultado de predicción */}
      {renderPredictionResult()}

      {/* Gráficos y análisis */}
      {derivativeResult && (
        <Tabs defaultValue="charts" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="charts">Gráficos</TabsTrigger>
            <TabsTrigger value="detector">Detector</TabsTrigger>
            <TabsTrigger value="analysis">Análisis</TabsTrigger>
            <TabsTrigger value="theory">Teoría</TabsTrigger>
          </TabsList>
          
          <TabsContent value="charts" className="space-y-4">
            <DerivativeChart
              derivativeResult={derivativeResult}
              seismicData={syntheticData?.data}
              showSteps={showSteps}
            />
          </TabsContent>
          
          <TabsContent value="detector" className="space-y-4">
            <AccelerationDetector
              derivativeResult={derivativeResult}
              predictionResult={predictionResult}
              isPreEruptionScenario={selectedScenario === 'pre_eruption'}
            />
          </TabsContent>
          
          <TabsContent value="analysis" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>🔍 Análisis Detallado</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-blue-800 mb-2">Estado de Actividad</h4>
                      <p className="text-sm text-blue-700">
                        {derivativeResult.acceleration_trend || 'ACTIVIDAD ESTABLE'}
                      </p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-green-800 mb-2">Aceleración Máxima</h4>
                      <p className="text-sm text-green-700">
                        {derivativeResult.max_acceleration?.toFixed(6) || '0.000000'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-gray-800 mb-2">Interpretación</h4>
                    <p className="text-sm text-gray-700">
                      {derivativeResult.acceleration_trend === "ACTIVIDAD ESTABLE" ?
                        'Los datos no muestran aceleración crítica en este momento. La actividad sísmica se encuentra dentro de parámetros normales. Continuar con monitoreo rutinario.' :
                        derivativeResult.acceleration_trend.includes("CRÍTICA") ?
                        'Se ha detectado aceleración crítica, lo que indica una posible presurización del sistema magmático. Esta es una señal que requiere monitoreo intensivo y evaluación inmediata.' :
                        derivativeResult.acceleration_trend.includes("MODERADA") ?
                        'Se detecta aceleración moderada en el sistema. Incrementar la frecuencia de monitoreo y mantener vigilancia.' :
                        'Se detecta aceleración leve. Mantener monitoreo regular y observar evolución de la tendencia.'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="theory" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>📚 Fundamentos Teóricos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">Modelo de Pronóstico de Falla (FFM)</h4>
                    <p className="text-sm text-blue-700">
                      El modelo FFM se basa en la premisa de que los sistemas volcánicos exhiben aceleración 
                      antes de la falla (erupción). La detección de esta aceleración mediante derivadas numéricas 
                      permite establecer ventanas de tiempo para posibles erupciones.
                    </p>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">Diferencias Finitas</h4>
                    <p className="text-sm text-green-700">
                      Las derivadas se calculan usando diferencias finitas sobre datos discretos de monitoreo. 
                      Este enfoque algorítmico permite operar directamente sobre datos reales o simulados 
                      sin necesidad de funciones continuas.
                    </p>
                  </div>
                  
                  <div className="bg-red-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">Señales Críticas</h4>
                    <p className="text-sm text-red-700">
                      Una segunda derivada positiva y creciente (S''(t) &gt; 0 y en aumento) es la señal 
                      clave que indica aceleración en la liberación de energía, interpretada como 
                      presurización del sistema magmático según Kilburn (2018).
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Enlace a documentación */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-2">
              Para más información técnica sobre la API de cálculo:
            </p>
            <Button
              variant="outline"
              onClick={() => window.open(calculoService.getApiDocsUrl(), '_blank')}
            >
              📖 Ver Documentación API
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CalculoVolcanico;