/**
 * 🌋 Volcano App Frontend - Servicio de Cálculos
 * Comunicación con el monolito Python para análisis de derivadas volcánicas
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// =====================================================
// TIPOS E INTERFACES
// =====================================================

export interface SeismicDataPoint {
  timestamp: string;
  magnitude: number;
  frequency: number;
  duration: number;
  depth: number;
}

export interface DerivativeCalculationRequest {
  data_points: number[];
  time_points: number[];
  show_steps: boolean;
}

export interface PredictionRequest {
  seismic_data: SeismicDataPoint[];
  time_window: number;
  calculation_detail: boolean;
}

export interface FFMAnalysisRequest {
  seismic_data: SeismicDataPoint[];
  time_window: number;
}

export interface CalculationStep {
  step_number: number;
  description: string;
  formula: string;
  calculation: string;
  result: number;
  explanation: string;
}

export interface DerivativeResult {
  first_derivative: number[];
  second_derivative: number[];
  acceleration_points: number[];
  max_acceleration: number;
  acceleration_trend: string;
  calculation_steps: CalculationStep[] | null;
  timestamp: string;
}

export interface PredictionResult {
  timestamp: string;
  eruption_probability: number;
  alert_level: 'GREEN' | 'YELLOW' | 'ORANGE' | 'RED';
  derivative_analysis: DerivativeResult;
  ffm_analysis: {
    failure_probability: number;
    time_to_failure?: number;
    acceleration_trend: string;
    confidence_level: number;
    critical_threshold: number;
    precursor_indicators: string[];
  };
  calculation_steps?: any[];
}

export interface SyntheticDataOptions {
  days: number;
  eruption_scenario: boolean;
  pattern_type?: 'linear' | 'exponential' | 'periodic';
  noise_level?: number;
}

export interface SyntheticDataResponse {
  data: SeismicDataPoint[];
  count: number;
  period_days: number;
  eruption_scenario: boolean;
  generated_at: string;
}

// =====================================================
// CONFIGURACIÓN DE AXIOS
// =====================================================

const CALCULO_API_BASE_URL = import.meta.env.VITE_CALCULO_API_BASE_URL || 'http://localhost:8000';

class CalculoService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: CALCULO_API_BASE_URL,
      timeout: 30000, // 30 segundos para cálculos complejos
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Interceptor para logging
    this.api.interceptors.request.use(
      (config) => {
        console.log(`🔢 Calculo API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('🔢 Calculo API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Interceptor para manejo de respuestas
    this.api.interceptors.response.use(
      (response) => {
        console.log(`🔢 Calculo API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('🔢 Calculo API Response Error:', error);
        return Promise.reject(error);
      }
    );
  }

  // =====================================================
  // CÁLCULO DE DERIVADAS
  // =====================================================

  /**
   * Calcular derivadas paso a paso
   */
  async calculateDerivatives(request: DerivativeCalculationRequest): Promise<DerivativeResult> {
    const response: AxiosResponse<DerivativeResult> = await this.api.post(
      '/calculate/derivatives',
      request
    );

    return response.data;
  }

  /**
   * Calcular derivadas desde datos sísmicos
   */
  async calculateDerivativesFromSeismic(
    seismicData: SeismicDataPoint[],
    useSteps: boolean = true
  ): Promise<DerivativeResult> {
    // Extraer magnitudes y crear puntos de tiempo
    const magnitudes = seismicData.map(d => d.magnitude);
    const timePoints = seismicData.map((_, index) => index);

    return this.calculateDerivatives({
      data_points: magnitudes,
      time_points: timePoints,
      show_steps: useSteps
    });
  }

  // =====================================================
  // PREDICCIÓN VOLCÁNICA
  // =====================================================

  /**
   * Realizar predicción completa de erupción volcánica
   */
  async predictEruption(request: PredictionRequest): Promise<PredictionResult> {
    const response: AxiosResponse<PredictionResult> = await this.api.post(
      '/predict',
      request
    );

    return response.data;
  }

  // =====================================================
  // ANÁLISIS FFM
  // =====================================================

  /**
   * Realizar análisis FFM (Failure Forecast Model)
   */
  async analyzeFFM(request: FFMAnalysisRequest): Promise<any> {
    const response: AxiosResponse<any> = await this.api.post(
      '/analyze/ffm',
      request
    );

    return response.data;
  }

  // =====================================================
  // GENERACIÓN DE DATOS SINTÉTICOS
  // =====================================================

  /**
   * Generar datos sintéticos para pruebas
   */
  async generateSyntheticData(options: SyntheticDataOptions): Promise<SyntheticDataResponse> {
    const params = new URLSearchParams({
      days: options.days.toString(),
      eruption_scenario: options.eruption_scenario.toString(),
      ...(options.pattern_type && { pattern_type: options.pattern_type }),
      ...(options.noise_level && { noise_level: options.noise_level.toString() })
    });

    const response: AxiosResponse<SyntheticDataResponse> = await this.api.get(
      `/data/generate?${params.toString()}`
    );

    return response.data;
  }

  /**
   * Generar datos de escenario pre-erupción
   */
  async generatePreEruptionScenario(days: number = 7): Promise<SyntheticDataResponse> {
    return this.generateSyntheticData({
      days,
      eruption_scenario: true,
      pattern_type: 'exponential'
    });
  }

  /**
   * Generar datos de actividad normal
   */
  async generateNormalActivity(days: number = 30): Promise<SyntheticDataResponse> {
    return this.generateSyntheticData({
      days,
      eruption_scenario: false,
      pattern_type: 'linear'
    });
  }

  // =====================================================
  // SALUD DEL SERVICIO
  // =====================================================

  /**
   * Verificar estado del servicio de cálculo
   */
  async checkHealth(): Promise<any> {
    const response: AxiosResponse<any> = await this.api.get('/health');
    return response.data;
  }

  /**
   * Obtener documentación de la API
   */
  getApiDocsUrl(): string {
    return `${CALCULO_API_BASE_URL}/docs`;
  }

  // =====================================================
  // UTILIDADES
  // =====================================================

  /**
   * Detectar aceleración sísmica en datos usando resultados del backend
   */
  async detectAcceleration(seismicData: SeismicDataPoint[]): Promise<{
    hasAcceleration: boolean;
    trend: 'increasing' | 'decreasing' | 'stable';
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    recommendations: string[];
  }> {
    const derivativeResult = await this.calculateDerivativesFromSeismic(seismicData);

    // Usar la clasificación del backend directamente
    const hasAcceleration = derivativeResult.acceleration_trend !== "ACTIVIDAD ESTABLE";

    // Determinar tendencia basada en la clasificación del backend
    const trend = derivativeResult.acceleration_trend.includes("POSITIVA") ? 'increasing' :
                  derivativeResult.acceleration_trend.includes("NEGATIVA") ? 'decreasing' : 'stable';

    // Evaluar riesgo usando la clasificación del backend
    const riskLevel = this.assessRiskFromBackend(derivativeResult.acceleration_trend);

    return {
      hasAcceleration,
      trend,
      riskLevel,
      recommendations: this.generateRecommendations(hasAcceleration, trend, riskLevel)
    };
  }

  /**
   * Analizar tendencia de valores
   */
  private analyzeTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';

    const increases = values.slice(1).filter((val, i) => val > values[i]).length;
    const decreases = values.slice(1).filter((val, i) => val < values[i]).length;

    if (increases > decreases * 1.5) return 'increasing';
    if (decreases > increases * 1.5) return 'decreasing';
    return 'stable';
  }

  /**
   * Evaluar nivel de riesgo basado en la clasificación del backend
   */
  private assessRiskFromBackend(accelerationTrend: string): 'low' | 'medium' | 'high' | 'critical' {
    if (accelerationTrend === "ACTIVIDAD ESTABLE") return 'low';
    if (accelerationTrend.includes("CRÍTICA")) return 'critical';
    if (accelerationTrend.includes("MODERADA")) return 'high';
    if (accelerationTrend.includes("LEVE")) return 'medium';
    return 'low';
  }

  /**
   * Evaluar nivel de riesgo (método legacy - mantener para compatibilidad)
   */
  private assessRisk(
    hasAcceleration: boolean,
    trend: 'increasing' | 'decreasing' | 'stable',
    maxAcceleration: number
  ): 'low' | 'medium' | 'high' | 'critical' {
    if (!hasAcceleration) return 'low';

    if (trend === 'increasing') {
      if (maxAcceleration > 5) return 'critical';
      if (maxAcceleration > 3) return 'high';
      return 'medium';
    }

    if (trend === 'stable' && maxAcceleration > 2) return 'medium';
    return 'low';
  }

  /**
   * Generar recomendaciones basadas en el análisis
   */
  private generateRecommendations(
    hasAcceleration: boolean,
    trend: 'increasing' | 'decreasing' | 'stable',
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
  ): string[] {
    const recommendations: string[] = [];

    if (hasAcceleration && trend === 'increasing') {
      recommendations.push('Aumentar la frecuencia de monitoreo sísmico');
      recommendations.push('Activar protocolos de alerta temprana');
    }

    if (riskLevel === 'high' || riskLevel === 'critical') {
      recommendations.push('Considerar evacuación preventiva de zonas de riesgo');
      recommendations.push('Coordinar con autoridades de emergencia');
    }

    if (trend === 'increasing') {
      recommendations.push('Monitorear otros precursores volcánicos (gases, deformación)');
    }

    if (recommendations.length === 0) {
      recommendations.push('Mantener monitoreo rutinario');
    }

    return recommendations;
  }

  // =====================================================
  // MANEJO DE ERRORES
  // =====================================================

  /**
   * Procesar errores de la API de cálculo
   */
  handleApiError(error: any): string {
    if (error.response) {
      const message = error.response.data?.detail || error.response.data?.message;
      if (message) return message;

      switch (error.response.status) {
        case 400:
          return 'Datos de entrada inválidos para el cálculo';
        case 422:
          return 'Error de validación en los parámetros';
        case 500:
          return 'Error interno en el servidor de cálculo';
        case 503:
          return 'Servicio de cálculo temporalmente no disponible';
        default:
          return `Error del servidor de cálculo (${error.response.status})`;
      }
    } else if (error.request) {
      return 'No se pudo conectar con el servicio de cálculo. Verifica que esté ejecutándose en el puerto 8000';
    } else {
      return error.message || 'Error inesperado en el servicio de cálculo';
    }
  }
}

// =====================================================
// INSTANCIA SINGLETON
// =====================================================

export const calculoService = new CalculoService();
export default calculoService;