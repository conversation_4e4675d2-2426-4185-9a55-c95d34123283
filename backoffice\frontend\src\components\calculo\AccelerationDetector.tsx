/**
 * 🌋 Volcano App Frontend - Detector de Aceleración
 * Componente especializado para detectar y visualizar aceleración sísmica crítica
 */

import {
    Activity,
    AlertOctagon,
    AlertTriangle,
    CheckCircle,
    Shield,
    TrendingUp
} from 'lucide-react';
import React, { useMemo } from 'react';
import { DerivativeResult, PredictionResult } from '../../services/calculoService';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';

interface AccelerationDetectorProps {
  derivativeResult: DerivativeResult;
  predictionResult?: PredictionResult | null;
  className?: string;
  isPreEruptionScenario?: boolean; // Nueva prop para detectar escenarios pre-eruptivos
}

interface AccelerationAnalysis {
  hasAcceleration: boolean;
  trend: 'increasing' | 'decreasing' | 'stable';
  severity: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
  criticalPoints: number[];
  recommendedActions: string[];
  timeToAction: number;
}

export const AccelerationDetector: React.FC<AccelerationDetectorProps> = ({
  derivativeResult,
  predictionResult,
  className,
  isPreEruptionScenario = false
}) => {
  
  // Análisis detallado de aceleración usando resultados del backend
  const analysis = useMemo((): AccelerationAnalysis => {
    const maxAcceleration = derivativeResult.max_acceleration || 0;
    const accelerationTrend = derivativeResult.acceleration_trend || "ACTIVIDAD ESTABLE";

    // Usar la clasificación del backend directamente
    const hasAcceleration = accelerationTrend !== "ACTIVIDAD ESTABLE";

    // Determinar tendencia basada en la clasificación del backend
    const trend = accelerationTrend.includes("POSITIVA") ? 'increasing' :
                  accelerationTrend.includes("NEGATIVA") ? 'decreasing' : 'stable';

    // Calcular severidad y score de riesgo
    let severity: 'low' | 'medium' | 'high' | 'critical' = 'low';
    let riskScore = 0;

    // PRIORIDAD 1: Si es escenario pre-eruptivo, forzar evaluación de alto riesgo
    if (isPreEruptionScenario) {
      // Para escenarios pre-eruptivos, evaluar basado en la aceleración detectada
      if (accelerationTrend.includes("CRÍTICA")) {
        severity = 'critical';
        riskScore = 90 + Math.min(10, maxAcceleration);
      } else if (accelerationTrend.includes("MODERADA") || maxAcceleration > 2.0) {
        severity = 'high';
        riskScore = 75 + Math.min(20, maxAcceleration * 5);
      } else if (accelerationTrend.includes("LEVE") || maxAcceleration > 1.0) {
        severity = 'medium';
        riskScore = 50 + Math.min(25, maxAcceleration * 10);
      } else {
        // Incluso actividad "estable" en pre-erupción debe ser al menos media
        severity = 'medium';
        riskScore = 40 + Math.min(20, maxAcceleration * 15);
      }
    }
    // PRIORIDAD 2: Si tenemos predicción completa, usar el nivel de alerta del sistema
    else if (predictionResult?.alert_level) {
      const alertLevel = predictionResult.alert_level;
      const probability = predictionResult.eruption_probability || 0;

      // Mapear niveles de alerta del backend a severidad del frontend
      if (alertLevel === 'RED') {
        severity = 'critical';
        riskScore = 85 + Math.min(15, probability * 100);
      } else if (alertLevel === 'ORANGE') {
        severity = 'high';
        riskScore = 60 + Math.min(25, probability * 100);
      } else if (alertLevel === 'YELLOW') {
        severity = 'medium';
        riskScore = 30 + Math.min(30, probability * 100);
      } else {
        severity = 'low';
        riskScore = Math.min(25, probability * 100);
      }
    }
    // PRIORIDAD 3: Fallback a análisis de derivadas si no hay predicción completa
    else {
      // Usar los mismos umbrales que el backend (5.0, 2.0, 1.0)
      if (accelerationTrend.includes("CRÍTICA")) {
        severity = 'critical';
        riskScore = 85 + Math.min(15, maxAcceleration);
      } else if (accelerationTrend.includes("MODERADA")) {
        severity = 'high';
        riskScore = 60 + Math.min(25, maxAcceleration * 10);
      } else if (accelerationTrend.includes("LEVE")) {
        severity = 'medium';
        riskScore = 30 + Math.min(30, maxAcceleration * 20);
      } else {
        severity = 'low';
        riskScore = Math.min(25, maxAcceleration * 25);
      }
    }
    
    // Identificar puntos críticos usando umbrales del backend (5.0 para crítico)
    const criticalPoints = derivativeResult.second_derivative
      .map((val, index) => ({ value: val, index }))
      .filter(point => point.value > 5.0)
      .map(point => point.index);
    
    // Generar recomendaciones basadas en el nivel de riesgo real
    const recommendedActions: string[] = [];

    // Generar recomendaciones basadas en el contexto (escenario pre-eruptivo vs predicción real)
    if (isPreEruptionScenario) {
      // Recomendaciones específicas para escenarios de entrenamiento pre-eruptivos
      if (severity === 'critical') {
        recommendedActions.push('🚨 ESCENARIO CRÍTICO: Activar protocolos de emergencia inmediatos');
        recommendedActions.push('📢 Evacuar zonas de alto riesgo según plan de contingencia');
        recommendedActions.push('🔄 Monitoreo continuo cada 15 minutos');
        recommendedActions.push('📞 Contactar autoridades de emergencia');
      } else if (severity === 'high') {
        recommendedActions.push('⚠️ ESCENARIO ALTO RIESGO: Incrementar vigilancia intensiva');
        recommendedActions.push('📋 Preparar planes de evacuación');
        recommendedActions.push('🔄 Monitoreo cada 30 minutos');
        recommendedActions.push('📞 Alertar a equipos de respuesta');
      } else if (severity === 'medium') {
        recommendedActions.push('📊 ESCENARIO MODERADO: Mantener monitoreo intensificado');
        recommendedActions.push('📋 Revisar protocolos de respuesta');
        recommendedActions.push('🔄 Monitoreo cada hora');
      } else {
        recommendedActions.push('✅ ESCENARIO CONTROLADO: Continuar monitoreo de entrenamiento');
        recommendedActions.push('📈 Analizar patrones de evolución');
      }
    } else if (predictionResult?.alert_level) {
      const alertLevel = predictionResult.alert_level;
      const probability = (predictionResult.eruption_probability || 0) * 100;

      if (alertLevel === 'RED') {
        recommendedActions.push(`🚨 ALERTA ROJA: Probabilidad de erupción ${probability.toFixed(1)}%`);
        recommendedActions.push('📞 Activar protocolo de emergencia inmediatamente');
        recommendedActions.push('🏃‍♂️ Evaluar evacuación de zonas de alto riesgo');
        recommendedActions.push('📡 Monitoreo continuo en tiempo real');
      } else if (alertLevel === 'ORANGE') {
        recommendedActions.push(`⚠️ ALERTA NARANJA: Probabilidad de erupción ${probability.toFixed(1)}%`);
        recommendedActions.push('📊 Incrementar frecuencia de monitoreo sísmico');
        recommendedActions.push('👥 Preparar equipos de respuesta de emergencia');
        recommendedActions.push('📢 Informar a comunidades en zonas de riesgo');
      } else if (alertLevel === 'YELLOW') {
        recommendedActions.push(`🟡 ALERTA AMARILLA: Probabilidad de erupción ${probability.toFixed(1)}%`);
        recommendedActions.push('📈 Incrementar monitoreo sísmico');
        recommendedActions.push('🔍 Analizar patrones de actividad volcánica');
        recommendedActions.push('📋 Revisar y actualizar planes de contingencia');
      } else {
        recommendedActions.push(`🟢 NIVEL VERDE: Probabilidad de erupción ${probability.toFixed(1)}%`);
        recommendedActions.push('✅ Mantener monitoreo rutinario');
        recommendedActions.push('📈 Continuar análisis de tendencias');
      }
    }
    // Fallback a recomendaciones basadas en derivadas
    else {
      if (severity === 'critical') {
        recommendedActions.push('🚨 Aceleración crítica detectada - Requiere evaluación inmediata');
        recommendedActions.push('📞 Notificar a autoridades de protección civil');
        recommendedActions.push('📡 Incrementar frecuencia de monitoreo a tiempo real');
      } else if (severity === 'high') {
        recommendedActions.push('⚠️ Aceleración moderada - Incrementar vigilancia');
        recommendedActions.push('📊 Aumentar frecuencia de monitoreo sísmico');
        recommendedActions.push('👥 Preparar equipos de respuesta');
      } else if (severity === 'medium') {
        recommendedActions.push('📈 Aceleración leve detectada - Mantener vigilancia');
        recommendedActions.push('🔍 Analizar patrones de actividad');
        recommendedActions.push('📋 Revisar planes de contingencia');
      } else {
        recommendedActions.push('✅ Mantener monitoreo rutinario');
        recommendedActions.push('📈 Continuar análisis de tendencias');
      }
    }
    
    // Calcular tiempo estimado para tomar acción
    const timeToAction = severity === 'critical' ? 0 :
                        severity === 'high' ? 2 :
                        severity === 'medium' ? 12 : 72;

    return {
      hasAcceleration,
      trend,
      severity,
      riskScore,
      criticalPoints,
      recommendedActions,
      timeToAction
    };
  }, [derivativeResult]);

  // Obtener color y icono según la severidad
  const getSeverityConfig = (severity: string) => {
    switch (severity) {
      case 'critical':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          icon: AlertOctagon,
          label: 'CRÍTICO'
        };
      case 'high':
        return {
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          icon: AlertTriangle,
          label: 'ALTO'
        };
      case 'medium':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          icon: Activity,
          label: 'MEDIO'
        };
      default:
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          icon: CheckCircle,
          label: 'BAJO'
        };
    }
  };

  const severityConfig = getSeverityConfig(analysis.severity);
  const IconComponent = severityConfig.icon;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Alerta Principal */}
      <Alert className={`${severityConfig.bgColor} ${severityConfig.borderColor}`}>
        <IconComponent className={`h-5 w-5 ${severityConfig.color}`} />
        <AlertDescription>
          <div className="flex items-center justify-between">
            <div>
              <strong className={severityConfig.color}>
                Nivel de Riesgo: {severityConfig.label}
              </strong>
              <div className="text-sm mt-1">
                {isPreEruptionScenario ? (
                  // Mostrar información específica para escenarios de entrenamiento
                  analysis.severity === 'critical' ?
                  `ESCENARIO PRE-ERUPTIVO CRÍTICO (Score: ${analysis.riskScore}%) - Simulación de activación de protocolos de emergencia` :
                  analysis.severity === 'high' ?
                  `ESCENARIO PRE-ERUPTIVO ALTO RIESGO (Score: ${analysis.riskScore}%) - Simulación de incremento de vigilancia` :
                  analysis.severity === 'medium' ?
                  `ESCENARIO PRE-ERUPTIVO MODERADO (Score: ${analysis.riskScore}%) - Simulación de monitoreo intensificado` :
                  `ESCENARIO PRE-ERUPTIVO CONTROLADO (Score: ${analysis.riskScore}%) - Simulación de monitoreo de entrenamiento`
                ) : predictionResult?.alert_level ? (
                  // Mostrar información basada en la predicción completa
                  predictionResult.alert_level === 'RED' ?
                  `Riesgo crítico de erupción (${((predictionResult.eruption_probability || 0) * 100).toFixed(1)}%) - Activación inmediata de protocolos de emergencia` :
                  predictionResult.alert_level === 'ORANGE' ?
                  `Riesgo alto de erupción (${((predictionResult.eruption_probability || 0) * 100).toFixed(1)}%) - Incrementar vigilancia y preparar respuesta` :
                  predictionResult.alert_level === 'YELLOW' ?
                  `Riesgo moderado de erupción (${((predictionResult.eruption_probability || 0) * 100).toFixed(1)}%) - Mantener monitoreo intensificado` :
                  `Riesgo bajo de erupción (${((predictionResult.eruption_probability || 0) * 100).toFixed(1)}%) - Continuar monitoreo rutinario`
                ) : (
                  // Fallback a información de derivadas
                  derivativeResult.acceleration_trend === "ACTIVIDAD ESTABLE" ?
                  'No se detecta aceleración crítica - Actividad dentro de parámetros normales' :
                  derivativeResult.acceleration_trend.includes("CRÍTICA") ?
                  'Aceleración crítica detectada - Posible presurización del sistema magmático - Requiere evaluación inmediata' :
                  derivativeResult.acceleration_trend.includes("MODERADA") ?
                  'Aceleración moderada detectada - Incrementar frecuencia de monitoreo' :
                  'Aceleración leve detectada - Mantener vigilancia'
                )}
              </div>
            </div>
            <Badge variant={analysis.severity === 'critical' ? 'destructive' : 'outline'}>
              {analysis.riskScore}% riesgo
            </Badge>
          </div>
        </AlertDescription>
      </Alert>

      {/* Detalles del Análisis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Análisis de Aceleración Sísmica
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Medidor de Riesgo */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Score de Riesgo</span>
                <span className="text-sm font-bold">{analysis.riskScore}%</span>
              </div>
              <Progress 
                value={analysis.riskScore} 
                className="h-3"
              />
            </div>

            {/* Métricas Clave */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${analysis.hasAcceleration ? 'text-red-600' : 'text-green-600'}`}>
                  {analysis.hasAcceleration ? 'SÍ' : 'NO'}
                </div>
                <div className="text-xs text-gray-600">Aceleración</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  analysis.trend === 'increasing' ? 'text-red-600' : 
                  analysis.trend === 'decreasing' ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {analysis.trend === 'increasing' ? '↗️' : 
                   analysis.trend === 'decreasing' ? '↘️' : '→'}
                </div>
                <div className="text-xs text-gray-600">Tendencia</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {analysis.criticalPoints.length}
                </div>
                <div className="text-xs text-gray-600">Puntos Críticos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analysis.timeToAction}h
                </div>
                <div className="text-xs text-gray-600">Tiempo p/Acción</div>
              </div>
            </div>

            {/* Información de Predicción Completa */}
            {predictionResult && (
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg border border-purple-200">
                <h4 className="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Análisis de Predicción Volcánica (FFM)
                </h4>
                <div className="grid grid-cols-2 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {((predictionResult.eruption_probability || 0) * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">Probabilidad de Erupción</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      predictionResult.alert_level === 'RED' ? 'text-red-600' :
                      predictionResult.alert_level === 'ORANGE' ? 'text-orange-600' :
                      predictionResult.alert_level === 'YELLOW' ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {predictionResult.alert_level}
                    </div>
                    <div className="text-xs text-gray-600">Nivel de Alerta</div>
                  </div>
                </div>
                <p className="text-sm text-purple-700">
                  El modelo FFM (Failure Forecast Model) analiza patrones de aceleración sísmica para predecir
                  la probabilidad de erupción volcánica. Este análisis integra datos de derivadas, clustering
                  temporal y métricas de aceleración para proporcionar una evaluación completa del riesgo.
                </p>
              </div>
            )}

            {/* Interpretación Científica */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Interpretación Científica
              </h4>
              <p className="text-sm text-blue-700">
                {isPreEruptionScenario ? (
                  // Interpretación específica para escenarios de entrenamiento
                  analysis.severity === 'critical' ?
                  'SIMULACIÓN: Este escenario pre-eruptivo muestra patrones críticos de aceleración sísmica que en condiciones reales indicarían presurización avanzada del sistema magmático. Ideal para entrenar respuestas de emergencia.' :
                  analysis.severity === 'high' ?
                  'SIMULACIÓN: Este escenario pre-eruptivo presenta aceleración significativa que en condiciones reales requeriría incremento inmediato de vigilancia. Excelente para practicar protocolos de respuesta.' :
                  analysis.severity === 'medium' ?
                  'SIMULACIÓN: Este escenario pre-eruptivo muestra aceleración moderada típica de fases tempranas de activación volcánica. Útil para entrenar detección de patrones precursores.' :
                  'SIMULACIÓN: Este escenario pre-eruptivo presenta actividad controlada ideal para familiarizarse con herramientas de monitoreo y análisis de tendencias sísmicas.'
                ) : predictionResult ? (
                  // Interpretación basada en predicción completa
                  predictionResult.alert_level === 'RED' ?
                  'El análisis FFM indica una probabilidad crítica de erupción. La aceleración sísmica detectada sugiere presurización avanzada del sistema magmático con posible ruptura inminente del equilibrio. Se requiere activación inmediata de protocolos de emergencia.' :
                  predictionResult.alert_level === 'ORANGE' ?
                  'El modelo FFM detecta una probabilidad significativa de erupción. Los patrones de aceleración indican desestabilización del sistema volcánico. Se recomienda incrementar el nivel de vigilancia y preparar medidas de respuesta.' :
                  predictionResult.alert_level === 'YELLOW' ?
                  'El análisis FFM muestra una probabilidad moderada de actividad volcánica. Se detectan cambios en los patrones sísmicos que requieren monitoreo intensificado y revisión de planes de contingencia.' :
                  'El modelo FFM indica baja probabilidad de erupción inmediata. Los parámetros sísmicos se mantienen dentro de rangos normales, pero se recomienda continuar el monitoreo rutinario.'
                ) : (
                  // Interpretación basada solo en derivadas
                  analysis.hasAcceleration && analysis.trend === 'increasing' ?
                  'La segunda derivada positiva y creciente (S\'\'(t) > 0 y en aumento) indica una aceleración en la liberación de energía sísmica. Según el modelo de Kilburn (2018), esto sugiere presurización del sistema magmático y puede ser una señal temprana de actividad volcánica inminente.' :
                  'Los datos actuales no muestran aceleración crítica. El sistema volcánico se mantiene en parámetros normales, pero se recomienda continuar con el monitoreo rutinario para detectar cambios en el patrón sísmico.'
                )}
              </p>
            </div>

            {/* Recomendaciones */}
            <div className={`p-4 rounded-lg ${severityConfig.bgColor}`}>
              <h4 className={`font-semibold mb-2 ${severityConfig.color}`}>
                Acciones Recomendadas
              </h4>
              <ul className="space-y-1">
                {analysis.recommendedActions.map((action, index) => (
                  <li key={index} className={`text-sm ${severityConfig.color} flex items-start gap-2`}>
                    <span className="text-xs mt-1">•</span>
                    <span>{action}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Fórmula de Detección */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-800 mb-2">Criterios de Detección (Backend)</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div><strong>Actividad Estable:</strong> <code>max_accel ≤ 1.0</code></div>
                <div><strong>Aceleración Leve:</strong> <code>1.0 &lt; max_accel ≤ 2.0</code></div>
                <div><strong>Aceleración Moderada:</strong> <code>2.0 &lt; max_accel ≤ 5.0</code></div>
                <div><strong>Aceleración Crítica:</strong> <code>max_accel &gt; 5.0</code></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccelerationDetector;