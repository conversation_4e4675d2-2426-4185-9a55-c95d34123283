#!/usr/bin/env python3
"""
Script de prueba para verificar la integración frontend-backend
Verifica que el frontend muestre las clasificaciones correctas del backend
"""

import sys
from datetime import datetime

import requests


def test_frontend_backend_consistency():
    """Prueba la consistencia entre frontend y backend"""
    print("🔄 PRUEBA DE CONSISTENCIA FRONTEND-BACKEND")
    print("=" * 60)
    
    backend_url = "http://localhost:8000"
    frontend_url = "http://localhost:3001"
    
    try:
        # 1. Verificar que ambos servicios estén funcionando
        print("1. Verificando servicios...")
        
        # Backend
        backend_health = requests.get(f"{backend_url}/health", timeout=5)
        if backend_health.status_code == 200:
            print("   ✅ Backend funcionando")
        else:
            print("   ❌ Backend no disponible")
            return False
        
        # Frontend (verificar que responda)
        try:
            frontend_response = requests.get(frontend_url, timeout=5)
            if frontend_response.status_code == 200:
                print("   ✅ Frontend funcionando")
            else:
                print("   ❌ Frontend no disponible")
                return False
        except requests.exceptions.ConnectionError:
            print("   ❌ Frontend no disponible en puerto 3001")
            return False
        
        # 2. Probar escenario de actividad normal
        print("\n2. Probando escenario de actividad normal...")
        normal_data = requests.get(
            f"{backend_url}/data/generate",
            params={"days": 3, "eruption_scenario": False},
            timeout=10
        ).json()
        
        # Calcular derivadas
        prediction_response = requests.post(
            f"{backend_url}/predict",
            json={
                "seismic_data": normal_data["data"],
                "time_window": 24,
                "calculation_detail": True
            },
            timeout=15
        ).json()
        
        backend_trend = prediction_response['derivative_analysis']['acceleration_trend']
        backend_max_accel = prediction_response['derivative_analysis']['max_acceleration']
        
        print(f"   Backend - Tendencia: {backend_trend}")
        print(f"   Backend - Max aceleración: {backend_max_accel:.6f}")
        
        # Verificar que actividad normal se clasifique correctamente
        if backend_trend == "ACTIVIDAD ESTABLE":
            print("   ✅ Backend clasifica actividad normal correctamente")
        else:
            print(f"   ❌ Backend clasifica actividad normal incorrectamente: {backend_trend}")
            return False
        
        # 3. Probar escenario pre-erupción
        print("\n3. Probando escenario pre-erupción...")
        eruption_data = requests.get(
            f"{backend_url}/data/generate",
            params={"days": 3, "eruption_scenario": True},
            timeout=10
        ).json()
        
        # Calcular derivadas
        eruption_response = requests.post(
            f"{backend_url}/predict",
            json={
                "seismic_data": eruption_data["data"],
                "time_window": 24,
                "calculation_detail": True
            },
            timeout=15
        ).json()
        
        eruption_trend = eruption_response['derivative_analysis']['acceleration_trend']
        eruption_max_accel = eruption_response['derivative_analysis']['max_acceleration']
        
        print(f"   Backend - Tendencia: {eruption_trend}")
        print(f"   Backend - Max aceleración: {eruption_max_accel:.6f}")
        
        # Verificar que actividad pre-erupción se detecte (cualquier nivel de aceleración)
        if eruption_trend != "ACTIVIDAD ESTABLE":
            print("   ✅ Backend detecta actividad pre-erupción correctamente")
        else:
            print(f"   ❌ Backend no detecta actividad pre-erupción: {eruption_trend}")
            return False
        
        # 4. Verificar endpoint de derivadas directamente
        print("\n4. Probando endpoint de derivadas directamente...")
        derivative_response = requests.post(
            f"{backend_url}/calculate/derivatives",
            json={
                "data_points": [2.0, 2.1, 2.2, 2.3, 2.4, 2.5],
                "time_points": [0, 1, 2, 3, 4, 5],
                "show_steps": False
            },
            timeout=10
        ).json()
        
        direct_trend = derivative_response['acceleration_trend']
        direct_max_accel = derivative_response['max_acceleration']
        
        print(f"   Tendencia directa: {direct_trend}")
        print(f"   Max aceleración directa: {direct_max_accel:.6f}")
        
        if direct_trend == "ACTIVIDAD ESTABLE":
            print("   ✅ Endpoint directo funciona correctamente")
        else:
            print(f"   ❌ Endpoint directo mal clasificado: {direct_trend}")
            return False
        
        # 5. Verificar que los tipos del frontend coincidan
        print("\n5. Verificando estructura de respuestas...")
        
        required_fields = ['max_acceleration', 'acceleration_trend']
        derivative_analysis = prediction_response['derivative_analysis']
        
        for field in required_fields:
            if field in derivative_analysis:
                print(f"   ✅ Campo '{field}' presente en respuesta")
            else:
                print(f"   ❌ Campo '{field}' faltante en respuesta")
                return False
        
        # 6. Generar reporte de consistencia
        print("\n6. Reporte de consistencia:")
        print(f"   📊 Actividad normal: {backend_trend} (max: {backend_max_accel:.6f})")
        print(f"   📊 Actividad pre-erupción: {eruption_trend} (max: {eruption_max_accel:.6f})")
        print(f"   📊 Datos lineales: {direct_trend} (max: {direct_max_accel:.6f})")
        
        # Verificar umbrales
        print("\n7. Verificando umbrales:")
        if backend_max_accel <= 1.0 and backend_trend == "ACTIVIDAD ESTABLE":
            print("   ✅ Umbral actividad estable (≤ 1.0) funcionando")
        else:
            print(f"   ⚠️  Verificar umbral actividad estable: {backend_max_accel}")
        
        if eruption_max_accel > 1.0 and eruption_trend != "ACTIVIDAD ESTABLE":
            print("   ✅ Detección de aceleración (> 1.0) funcionando")
        else:
            print(f"   ⚠️  Verificar detección de aceleración: {eruption_max_accel}")
        
        print("\n🎉 ¡TODAS LAS PRUEBAS DE CONSISTENCIA PASARON!")
        print("\n📋 INSTRUCCIONES PARA VERIFICAR EN EL FRONTEND:")
        print("1. Abrir http://localhost:3001 en el navegador")
        print("2. Navegar a la sección de Cálculo Volcánico")
        print("3. Generar datos con 'Actividad Normal'")
        print("4. Verificar que muestre 'ACTIVIDAD ESTABLE' (no 'Aceleración Detectada')")
        print("5. Generar datos con 'Pre-Erupción'")
        print("6. Verificar que muestre 'ACELERACIÓN CRÍTICA' o 'MODERADA'")
        print("7. Verificar que todas las alertas estén en español")
        
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Error de conexión: {str(e)}")
        return False
    except requests.exceptions.Timeout:
        print("❌ Timeout en la conexión")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {str(e)}")
        return False

def main():
    """Función principal"""
    print(f"🕐 Iniciando pruebas a las {datetime.now().strftime('%H:%M:%S')}")
    
    success = test_frontend_backend_consistency()
    
    if success:
        print("\n✅ INTEGRACIÓN FRONTEND-BACKEND EXITOSA")
        print("El sistema está listo para uso en producción")
        sys.exit(0)
    else:
        print("\n❌ PROBLEMAS DE INTEGRACIÓN DETECTADOS")
        print("Revisar configuración y correcciones")
        sys.exit(1)

if __name__ == "__main__":
    main()
