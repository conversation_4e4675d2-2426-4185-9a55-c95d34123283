#!/usr/bin/env python3
"""
Script de debug para verificar las respuestas de la API
"""

import requests
import json

def debug_api():
    """Debug de las respuestas de la API"""
    
    print("🔍 DEBUG DE LA API")
    print("=" * 40)
    
    # 1. Generar datos pequeños para debug
    print("🔄 Generando datos de prueba...")
    data_response = requests.get('http://localhost:8000/data/generate?days=3&eruption_scenario=true')
    data = data_response.json()
    print(f"✅ Datos generados: {data['count']} eventos")
    
    # 2. Probar endpoint de derivadas
    print("\n🔄 Probando endpoint de derivadas...")
    try:
        derivative_request = {
            'seismic_data': data['data'][:10],  # Solo primeros 10 eventos
            'show_steps': False
        }
        
        print("Enviando request:", json.dumps(derivative_request, indent=2, default=str)[:200] + "...")
        
        derivative_response = requests.post('http://localhost:8000/calculate/derivatives', 
                                          json=derivative_request)
        
        print(f"Status code: {derivative_response.status_code}")
        
        if derivative_response.status_code == 200:
            derivative_result = derivative_response.json()
            print("Respuesta de derivadas:")
            print(json.dumps(derivative_result, indent=2, default=str)[:500] + "...")
        else:
            print("Error response:", derivative_response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # 3. Probar endpoint de predicción completa
    print("\n🔄 Probando endpoint de predicción completa...")
    try:
        prediction_request = {
            'seismic_data': data['data'][:10],  # Solo primeros 10 eventos
            'time_window': 24,
            'calculation_detail': False
        }
        
        prediction_response = requests.post('http://localhost:8000/predict', 
                                          json=prediction_request)
        
        print(f"Status code: {prediction_response.status_code}")
        
        if prediction_response.status_code == 200:
            prediction_result = prediction_response.json()
            print("Respuesta de predicción:")
            print(json.dumps(prediction_result, indent=2, default=str)[:500] + "...")
        else:
            print("Error response:", prediction_response.text)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_api()
